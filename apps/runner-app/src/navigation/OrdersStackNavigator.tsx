import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'

import type { OrdersStackParamList } from '../types/navigation'
import { useTheme } from '../providers/ThemeProvider'

// Import Orders Screens (placeholders for now)
import { OrdersListScreen } from '../screens/orders/OrdersListScreen'

const Stack = createNativeStackNavigator<OrdersStackParamList>()

export function OrdersStackNavigator() {
  const { theme } = useTheme()

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.white,
        },
        headerTintColor: theme.colors.gray[900],
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerShadowVisible: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen
        name="OrdersList"
        component={OrdersListScreen}
        options={{
          title: 'Orders',
          headerShown: false, // Custom header in the screen
        }}
      />
    </Stack.Navigator>
  )
}
