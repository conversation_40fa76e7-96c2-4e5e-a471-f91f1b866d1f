import React from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Package, Clock, MapPin, DollarSign } from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'

export function OrdersListScreen() {
  const { theme } = useTheme()

  // Mock orders data
  const orders = [
    {
      id: '1',
      customerName: 'Sarah Johnson',
      restaurant: 'KFC Soweto',
      address: '123 Vilakazi Street, Orlando West',
      distance: '2.3 km',
      estimatedTime: '15 min',
      amount: 45.50,
      status: 'pending',
      items: 3,
    },
    {
      id: '2',
      customerName: '<PERSON>',
      restaurant: 'Nandos Maponya Mall',
      address: '456 Klipspruit Valley Road',
      distance: '1.8 km',
      estimatedTime: '12 min',
      amount: 67.20,
      status: 'accepted',
      items: 2,
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return theme.colors.warning[600]
      case 'accepted':
        return theme.colors.primary[600]
      case 'picked_up':
        return theme.colors.secondary[600]
      case 'delivered':
        return theme.colors.success[600]
      default:
        return theme.colors.gray[600]
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'New Order'
      case 'accepted':
        return 'Accepted'
      case 'picked_up':
        return 'Picked Up'
      case 'delivered':
        return 'Delivered'
      default:
        return status
    }
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.gray[900] }]}>
          Orders
        </Text>
        <Text style={[styles.headerSubtitle, { color: theme.colors.gray[600] }]}>
          {orders.length} orders available
        </Text>
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
        {orders.length > 0 ? (
          orders.map((order) => (
            <TouchableOpacity
              key={order.id}
              style={[styles.orderCard, { backgroundColor: theme.colors.white }]}
              activeOpacity={0.7}
            >
              {/* Order Header */}
              <View style={styles.orderHeader}>
                <View style={styles.orderInfo}>
                  <Text style={[styles.customerName, { color: theme.colors.gray[900] }]}>
                    {order.customerName}
                  </Text>
                  <Text style={[styles.restaurantName, { color: theme.colors.gray[600] }]}>
                    {order.restaurant}
                  </Text>
                </View>
                
                <View style={[
                  styles.statusBadge,
                  { backgroundColor: `${getStatusColor(order.status)}20` }
                ]}>
                  <Text style={[
                    styles.statusText,
                    { color: getStatusColor(order.status) }
                  ]}>
                    {getStatusText(order.status)}
                  </Text>
                </View>
              </View>

              {/* Order Details */}
              <View style={styles.orderDetails}>
                <View style={styles.detailRow}>
                  <MapPin size={16} color={theme.colors.gray[500]} />
                  <Text style={[styles.detailText, { color: theme.colors.gray[600] }]}>
                    {order.address}
                  </Text>
                </View>

                <View style={styles.detailRow}>
                  <Clock size={16} color={theme.colors.gray[500]} />
                  <Text style={[styles.detailText, { color: theme.colors.gray[600] }]}>
                    {order.estimatedTime} • {order.distance}
                  </Text>
                </View>

                <View style={styles.detailRow}>
                  <Package size={16} color={theme.colors.gray[500]} />
                  <Text style={[styles.detailText, { color: theme.colors.gray[600] }]}>
                    {order.items} items
                  </Text>
                </View>
              </View>

              {/* Order Footer */}
              <View style={styles.orderFooter}>
                <View style={styles.amountContainer}>
                  <DollarSign size={18} color={theme.colors.success[600]} />
                  <Text style={[styles.amount, { color: theme.colors.success[600] }]}>
                    R{order.amount.toFixed(2)}
                  </Text>
                </View>

                {order.status === 'pending' && (
                  <TouchableOpacity
                    style={[styles.acceptButton, { backgroundColor: theme.colors.primary[600] }]}
                  >
                    <Text style={[styles.acceptButtonText, { color: theme.colors.white }]}>
                      Accept Order
                    </Text>
                  </TouchableOpacity>
                )}

                {order.status === 'accepted' && (
                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: theme.colors.secondary[600] }]}
                  >
                    <Text style={[styles.actionButtonText, { color: theme.colors.white }]}>
                      Start Pickup
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </TouchableOpacity>
          ))
        ) : (
          <View style={[styles.emptyState, { backgroundColor: theme.colors.white }]}>
            <Package size={64} color={theme.colors.gray[400]} />
            <Text style={[styles.emptyTitle, { color: theme.colors.gray[900] }]}>
              No Orders Available
            </Text>
            <Text style={[styles.emptySubtitle, { color: theme.colors.gray[600] }]}>
              Turn on your online status to start receiving orders from customers in your area.
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  orderCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  orderInfo: {
    flex: 1,
  },
  customerName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  restaurantName: {
    fontSize: 14,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  orderDetails: {
    marginBottom: 16,
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 14,
    flex: 1,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  amount: {
    fontSize: 18,
    fontWeight: '700',
  },
  acceptButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  acceptButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
    borderRadius: 12,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
})
