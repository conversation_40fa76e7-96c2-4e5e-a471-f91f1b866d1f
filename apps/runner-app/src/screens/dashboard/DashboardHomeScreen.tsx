import React from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { 
  MapPin, 
  Clock, 
  DollarSign, 
  Package,
  Star,
  TrendingUp,
  Bell
} from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import { useAuthStore } from '../../stores/authStore'

export function DashboardHomeScreen() {
  const { theme } = useTheme()
  const { runner } = useAuthStore()
  const [isOnline, setIsOnline] = React.useState(false)

  // Mock data - will be replaced with real data
  const todayStats = {
    earnings: 245.50,
    deliveries: 8,
    hours: 6.5,
    rating: 4.8,
  }

  const quickActions = [
    {
      id: 'available-orders',
      title: 'Available Orders',
      subtitle: '3 orders nearby',
      icon: Package,
      color: theme.colors.primary[600],
      bgColor: theme.colors.primary[50],
    },
    {
      id: 'earnings',
      title: 'Today\'s Earnings',
      subtitle: `R${todayStats.earnings.toFixed(2)}`,
      icon: DollarSign,
      color: theme.colors.success[600],
      bgColor: theme.colors.success[50],
    },
    {
      id: 'location',
      title: 'Update Location',
      subtitle: 'Share your location',
      icon: MapPin,
      color: theme.colors.secondary[600],
      bgColor: theme.colors.secondary[50],
    },
  ]

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
          <View style={styles.headerContent}>
            <View>
              <Text style={[styles.greeting, { color: theme.colors.gray[600] }]}>
                Good morning,
              </Text>
              <Text style={[styles.runnerName, { color: theme.colors.gray[900] }]}>
                {runner?.name || 'Runner'}
              </Text>
            </View>
            
            <TouchableOpacity style={styles.notificationButton}>
              <Bell size={24} color={theme.colors.gray[600]} />
            </TouchableOpacity>
          </View>

          {/* Online Status Toggle */}
          <View style={[styles.statusCard, { backgroundColor: theme.colors.gray[50] }]}>
            <View style={styles.statusContent}>
              <View>
                <Text style={[styles.statusTitle, { color: theme.colors.gray[900] }]}>
                  {isOnline ? 'You\'re Online' : 'You\'re Offline'}
                </Text>
                <Text style={[styles.statusSubtitle, { color: theme.colors.gray[600] }]}>
                  {isOnline ? 'Ready to receive orders' : 'Turn on to start receiving orders'}
                </Text>
              </View>
              
              <Switch
                value={isOnline}
                onValueChange={setIsOnline}
                trackColor={{
                  false: theme.colors.gray[300],
                  true: theme.colors.primary[200],
                }}
                thumbColor={isOnline ? theme.colors.primary[600] : theme.colors.gray[500]}
              />
            </View>
          </View>
        </View>

        {/* Today's Stats */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
            Today's Performance
          </Text>
          
          <View style={styles.statsGrid}>
            <View style={[styles.statCard, { backgroundColor: theme.colors.white }]}>
              <DollarSign size={24} color={theme.colors.success[600]} />
              <Text style={[styles.statValue, { color: theme.colors.gray[900] }]}>
                R{todayStats.earnings.toFixed(2)}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.gray[600] }]}>
                Earnings
              </Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: theme.colors.white }]}>
              <Package size={24} color={theme.colors.primary[600]} />
              <Text style={[styles.statValue, { color: theme.colors.gray[900] }]}>
                {todayStats.deliveries}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.gray[600] }]}>
                Deliveries
              </Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: theme.colors.white }]}>
              <Clock size={24} color={theme.colors.secondary[600]} />
              <Text style={[styles.statValue, { color: theme.colors.gray[900] }]}>
                {todayStats.hours}h
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.gray[600] }]}>
                Hours
              </Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: theme.colors.white }]}>
              <Star size={24} color={theme.colors.warning[600]} />
              <Text style={[styles.statValue, { color: theme.colors.gray[900] }]}>
                {todayStats.rating}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.gray[600] }]}>
                Rating
              </Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
            Quick Actions
          </Text>
          
          <View style={styles.actionsGrid}>
            {quickActions.map((action) => (
              <TouchableOpacity
                key={action.id}
                style={[styles.actionCard, { backgroundColor: theme.colors.white }]}
                activeOpacity={0.7}
              >
                <View style={[styles.actionIcon, { backgroundColor: action.bgColor }]}>
                  <action.icon size={24} color={action.color} />
                </View>
                <Text style={[styles.actionTitle, { color: theme.colors.gray[900] }]}>
                  {action.title}
                </Text>
                <Text style={[styles.actionSubtitle, { color: theme.colors.gray[600] }]}>
                  {action.subtitle}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Recent Activity
            </Text>
            <TouchableOpacity>
              <Text style={[styles.seeAllText, { color: theme.colors.primary[600] }]}>
                See All
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={[styles.activityCard, { backgroundColor: theme.colors.white }]}>
            <Text style={[styles.activityText, { color: theme.colors.gray[600] }]}>
              No recent activity. Start accepting orders to see your delivery history here.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    marginBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  greeting: {
    fontSize: 16,
    marginBottom: 4,
  },
  runnerName: {
    fontSize: 24,
    fontWeight: '700',
  },
  notificationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusCard: {
    borderRadius: 12,
    padding: 16,
  },
  statusContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  statusSubtitle: {
    fontSize: 14,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  seeAllText: {
    fontSize: 16,
    fontWeight: '600',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCard: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
  },
  actionsGrid: {
    gap: 12,
  },
  actionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  actionSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  activityCard: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  activityText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
})
